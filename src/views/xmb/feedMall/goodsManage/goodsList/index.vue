<template>
    <div class="app-container">
        <el-card class="mb10 form_box" shadow="never" ref="formBox">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="98px">
                <el-row class="form_row">
                    <el-col class="form_col">
                        <el-form-item label="区域查询：" prop="areaDataRange">
                <RegCascaderTag v-model="queryParams.areaDataRange" :flag="false"></RegCascaderTag>
            </el-form-item>
                        <el-form-item label-width="120" label="商品名称" prop="goodsName">
                                <el-input v-model="queryParams.goodsName" placeholder="请输入商品名称" clearable  @keyup.enter.native="handleQuery" />
                            </el-form-item>
                            <el-form-item label="分类" prop="categoryIds">
                                <el-cascader :disabled="disabled" @change="categoryChange" v-model="queryParams.categoryIds"
                                    :props="categoryOptionProps" :options="categoryList" ref="category" placeholder="选择商品分类" clearable>
                                </el-cascader>
                            </el-form-item>
                            <el-form-item label="上架状态" prop="marketEnable">
                                <el-select v-model="queryParams.marketEnable" placeholder="请选择商品状态" @change="changeState">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option v-for="item in shopStatusList" :key="item.dictValue" :label="item.dictLabel"
                                        :value="item.dictValue" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="审核状态" prop="authFlag">
                                <el-select v-model="queryParams.authFlag" placeholder="请选择商品审核状态" @change="changeState">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option v-for="item in approveStatusList" :key="item.dictValue" :label="item.dictLabel"
                                        :value="item.dictValue" />
                                </el-select>
                            </el-form-item>
                        <el-form-item label="价格范围" class="priceFormItem" prop="minPrice" >
                            <el-input v-model="queryParams.minPrice" placeholder="最低" style="width: 101px" clearable @keyup.enter.native="handleQuery" />
                            -
                            <el-input v-model="queryParams.maxPrice" placeholder="最高" style="width: 101px" clearable @keyup.enter.native="handleQuery" />
                        </el-form-item>

                        <el-form-item label="成交量" class="priceFormItem" prop="minSalesNum" >
                            <el-input v-model="queryParams.minSalesNum" placeholder="最低" style="width: 101px" clearable @keyup.enter.native="handleQuery" />
                            -
                            <el-input v-model="queryParams.maxSalesNum" placeholder="最高" style="width: 101px" clearable @keyup.enter.native="handleQuery" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
            <el-col >
            <el-form-item >
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp" >
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i
                  :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                ></i>
              </el-button>
              </template>

            </el-form-item>
          </el-col>
        </el-row>
            </el-form>
        </el-card>


        <el-row :gutter="10" class="mb8" style="display: flex; justify-content: flex-end;">
            <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd(0)">发布商品</el-button>
            </el-col>
            <!-- <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd(1)">发布服务</el-button>
            </el-col> -->
        </el-row>
        <el-card shadow="never">
        <div style='display: flex;justify-content: space-between;'>
            <el-tabs @tab-click="tabClick" v-model="activeTab">
                <el-tab-pane v-for="tab in  tabList" :key='tab.idKey' :label="tab.tabName" :name="tab.idKey">
                </el-tab-pane>
            </el-tabs>


            <div>
                <span class="plcz">批量操作：</span>
                <el-select v-model="batchStatus" placeholder="选择批量操作" @change='batchChange' clearable
                    style="width: 130px">
                    <el-option label="选择操作" value=""></el-option>
                    <el-option label="批量下架" :value="0"></el-option>
                    <!-- <el-option label="批量上架" :value="1"></el-option> -->
                    <el-option label="批量删除" :value="2"></el-option>
                </el-select>
            </div>
        </div>

        <el-table v-loading="loading" ref="multipleTable" :data="list" type="selection"
        border
            @selection-change="tableSelectionChange" style="width: 100%" :height="tableHeight">
            <el-table-column type="selection" width="50">
            </el-table-column>
            <el-table-column label="商品名称" align="center" prop="goodsName" min-width="120" />
            <el-table-column label="图片" align="center">
                <template slot-scope="scope">
                    <div @click="bigImg(scope.row.images)">
                        <el-image fit="cover" style="width: 70px; height: 70px" :src="goodsImg(scope.row.images+'?x-image-process=style/xmb-thumb')"
                            :preview-src-list="imgList"></el-image>
                    </div>
                </template>
            </el-table-column>

            <el-table-column label="一级分类" align="center" prop="categoryFirst" :formatter="categoryFirstName" />
            <!-- <el-table-column label="二级分类" align="center" prop="categorySecond" :formatter="categorySecondName" /> -->
            <el-table-column label="二级分类" align="center" prop="categorySecondName">
                <template slot-scope="scope">
                   {{ scope.row.categorySecondName || '--' }}
                </template>
            </el-table-column>
            <el-table-column label="计量单位" align="center" prop="unitName" width='90px' />
            <el-table-column label="库存" align="center" prop="quantity" width='90px' :formatter="quantityName" />
            <el-table-column label="起购量" align="center" prop="salesModel" width='90px' :formatter="salesModelName" />
            <el-table-column label="成交单数" align="center" prop="salesVolumes" width='90px' :formatter="salesVolumesVal" />

            <el-table-column label="商品单价" align="center" prop="price" width='90px' />
            <el-table-column label="会员单价" align="center" prop="memberPrice" width='90px'>
                <template slot-scope="scope">
                    <span>{{scope.row.memberPrice ? scope.row.memberPrice : '--'}}</span>
                </template>
            </el-table-column>

            <el-table-column label="状态" align="center" prop="marketEnable" :formatter="marketEnableName">
            </el-table-column>
            <el-table-column label="审核状态" align="center" prop="authFlag" :formatter="authFlagName"> </el-table-column>
            <el-table-column label="发布时间" align="center" prop="createTime" min-width="180" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="180">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="handDetails(scope.row)" icon="el-icon-info">
                        详情
                    </el-button>
                    <el-button size="mini" v-if="scope.row.authFlag == 3 || scope.row.marketEnable == 3 || scope.row.marketEnable == 0" type="text"
                        icon="el-icon-edit" @click="edit(scope.row)">重新上架</el-button>

                    <el-button icon="el-icon-delete" v-if='scope.row.marketEnable != 2' size="mini" type="text"
                        @click="handleDelete(scope.row.goodsId)">删除</el-button>


                    <template v-if="scope.row.authFlag == 1">
                        <!-- <el-button v-if="scope.row.marketEnable == 3 || scope.row.marketEnable == 1"
                            icon="el-icon-upload2" size="mini" type="text" @click="handleUpper(scope.row.goodsId)">上架
                        </el-button> -->
                        <el-button v-if="scope.row.marketEnable == 2" icon="el-icon-download" size="mini" type="text"
                            @click="handleLower(scope.row.goodsId)">下架
                        </el-button>
                        <el-button v-if="scope.row.marketEnable == 2" icon="el-icon-edit" size="mini" type="text"
                            @click="handleEdit(scope.row)">编辑
                        </el-button>
                        <!-- v-hasPermi="['xmb:goods:editGoods']"-->
                    </template>


                </template>
            </el-table-column>
        </el-table>
        </el-card>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        <!-- 添加或修改配置对话框 -->
        <operating-form v-if='dialog' ref="openFrom" @close="close" @refresh="refreshList" @dialogSatus="dialogSatus"></operating-form>
        <operating-server-form v-if='dialog1' ref="openFrom1" @close="close" @refresh="refreshList" @dialogSatus="dialogSatus"></operating-server-form>
        <!-- 上架状态免审核编辑 -->
        <edit-form v-if='editdialog' ref="openEditFrom" @refresh="refreshList" @dialogSatus="dialogSatus"></edit-form>
    </div>
</template>

<script>
import { deleteGoodsByIds, gootsCategoryList, selectGoodsCount, goodsPut, goodsUp, shelves,  updateGoods } from "@/api/xmb/feedMall/goodsManage/goodsList";
import { storeList } from "@/api/xmb/feedMall/shop/createShop";
import { getGoodsList } from "@/api/xmb/feedMall/goodsManage/goodsList";
import { getDicts } from "@/api/system/dict/data.js";
import operatingForm from "./components/operatingForm";
import operatingServerForm from "./components/operatingServerForm.vue";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { repage } from "@/utils/repage.js"
import editForm from './components/editForm.vue'
import { tableUi } from "@/utils/mixin/tableUi.js";

export default {
    mixins:[tableUi],
    name: "goodsList",
    dicts: ["goods_approve_status"],
    components: {
        operatingForm,
        operatingServerForm,
        ElImageViewer,
        editForm
    },
    props:{
      sourceStoreId: {
        type: String,
        default: '',
      },
    },
    data() {
        return {
            marketEnable: '',
            imgList: [],
            number: "",
            // 遮罩层
            loading: true,
            // 总条数
            total: 0,
            // 表格数据
            list: [],
            pageNum: 1,
            pageSize: 10,
            disabled: false,
            //产品名称
            productNames: [],
            //图片位置
            positions: [],
            // 查询参数
            queryParams: {
                areaDataRange: "",
                pageNum: 1,
                pageSize: 10,
                tabActive: 0,
                goodsName: "",//商品名称
                storeName: "",//店铺名称
                authFlag: "",//审核状态（4未提交 1审核通过 2待审核 3 审核驳回）
                categoryFirst: "",//一级类型Id
                categoryFirstName: "",//一级类型名称
                categorySecond: "",//二级级类型Id
                categorySecondName: "",//二级类型名称
                minSalesNum: '',//最低销售量
                maxSalesNum: '',//最高销售量
                minPrice: '',//最低价
                maxPrice: '',//最高价
                marketEnable: '',//marketEnable:上架状态（1草稿 2上架 3下架）
                sortBy: 'create_time',
                sortType: 'desc',
                tenantId: "",
                storeId:'',
                goodsType: 0
            },
            createBy:'',
            batchStatus: '',
            shopExistence: false,
            // 分类菜单
            categoryList: [],
            categoryOptionProps: {
                value: 'goodsCategoryId',
                label: 'name'
            },
            // 选择的分类菜单信息
            selectCategory: {
                ids: [],
                name: []
            },
            //商品状态数量统计
            goodsStaStatusCount: {
                draftCount: "0",//草稿
                onSaleCount: "0",//出售中
                solDoutCount: "0",//已售空
                underReviewCount: "0",//审核中
                dshelfCount: "0",//待上架
                // totalCount: '0'
            },
            //审核状态数据
            approveStatusList: [],
            //商品状态数据字典
            shopStatusList: [],
            // 表格选中的ID
            multipleSelection: [],
            dialog: true,
            dialog1:true,
            editdialog: true,
            tabList: [],
            activeTab: '-51a'
        };
    },
    created() {
        let {tenantId,userName, userId} = this.$store.state.user.user;
        let {storeId=''} = this.$store.state.user.store;
        this.queryParams.tenantId = tenantId || '';
        this.queryParams.storeId = storeId || '';
        this.createBy = userId
        if(userName=='xmadmin'||userName=='admin'){
          this.queryParams.storeId =''
        }
        this.getList();
        this.getDictData();
    },

    computed: {
        //商品图
        goodsImg() {
            return (imgs) => {
                if (imgs == '') {
                    return ''
                }
                imgs = imgs?.split(',');
                return imgs[0]
            }
        },
        //审核名称
        authFlagName() {
            let dictLabel = ''
            return (val) => {
                this.approveStatusList.forEach((item) => {
                    if (item.dictValue == val.authFlag) {
                        dictLabel = item.dictLabel;
                    }
                });
                return dictLabel;
            };
        },

        marketEnableName() {

            return (val) => {
              let dictLabel = '--'
              console.log(val, val.marketEnable)
              console.log(this.shopStatusList)
                this.shopStatusList.forEach((item) => {

                    if (item.dictValue == val.marketEnable) {
                        dictLabel = item.dictLabel;
                    }
                });
                return dictLabel;
            };
        },
        salesVolumesVal() {
            return (row, com, val) => {
                return val ? val : 0;
            };
        },
        // 起购数量
        salesModelName() {
            return (row, com, val) => {
                if (row.isSalescount == 2) {
                    return '1'
                } else {
                    return val
                }
            }
        },

        // 库存
        quantityName() {
            return (row, com, val) => {
                if(row.goodsType==1) {
                    return '--'
                } else {
                    if (row.isStock == 2 || row.quantity == '-1') {
                    return '不限'
                } else {
                    return val
                }
                }

            }
        },



        // 一级分类菜单名字
        categoryFirstName() {
            return (val) => {
                let dictLabel = ""
                this.categoryList.forEach((item) => {
                    if (item.goodsCategoryId == val.categoryFirst) {
                        dictLabel = item.name;
                    }
                });
                return dictLabel;
            };
        },


        // 二级分类菜单名称
        categorySecondName() {
            return (val) => {
                let dictLabel = ""
                this.categoryList.forEach((item) => {
                    let children = item.children || [];
                    children.forEach(childrenItem => {
                        if (childrenItem.goodsCategoryId == val.categorySecond) {
                            dictLabel = childrenItem.name;
                        }
                    })
                });
                return dictLabel;
            };
        }

    },
    methods: {
        close(){
            this.$refs.openFrom1.open = false;
            this.$refs.openFrom.open = false;
        },
        changeState() {
            this.activeTab = '-51a';
        },
        //查询当前企业是否存在店铺
        getShop() {
            return new Promise((resolve, reject) => {
                // let phonenumber = this.$store.state.user.phonenumber;
                storeList({
                    storeType: 5,
                    userId: this.createBy
                }).then(shop => {
                    if (!shop.result || shop?.result?.storeStatus != 7) {
                        this.$confirm('未开通店铺，无法新增商品', '提示', {
                            confirmButtonText: '确定',
                            type: 'warning',
                            showClose: true
                        })
                        resolve('');
                        return
                    }
                    if (shop.code == 200) {
                        console.log(shop.result);
                        localStorage.setItem('storeAreaDataRange', JSON.stringify(shop.result.areaDataRange));
                        resolve(shop.result)
                    }
                })

            })
        },

        //获取弹窗关闭状态
        dialogSatus(status) {
            this.dialog = status;
            this.dialog1=status
            this.editdialog = status
            setTimeout(() => {
                this.dialog = true;
                this.dialog1=true
                this.editdialog = true;
            }, 1000)
        },
        //请求数据字典数据
        async getDictData() {
            //商品分类列表 全部分类
            gootsCategoryList({categoryType:0}).then((res) => {
                if (res.code == 200) {
                    this.categoryList = res.result.list
                }
            });
            //请求审核状态数据字典数据
            let approveStatus = await getDicts('goods_approve_status');
            if (approveStatus) {
                let result = approveStatus.data || [];
                this.approveStatusList = result;
            }
            // 商品状态
            let shopStatus = await getDicts('shop_status');
            if (shopStatus) {
                let result = shopStatus.data || [];
                this.shopStatusList = result;
            };
            this.getTabList()
        },
        //点击列表商品图片
        bigImg(url) {
            this.imgList = [];
            if (url == '') {
                return
            }
            this.imgList = url.split(',')
        },

        //刷新页面
        refreshList() {
            this.getList();
            this.getTabList()
        },
        //删除
        handleDelete(goodsId) {
            this.$confirm('确定删除该商品吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.delGoods(goodsId.split(','))
            }).catch(() => {
            });
        },
        // 上架
        handleUpper(goodsId) {
            console.log(goodsId)
            goodsPut({ goodsId: [goodsId] }).then((res) => {
                if (res.code == 200) {
                    this.refreshList()
                    this.$message({
                        type: 'success',
                        message: '上架成功!'
                    });
                } else {
                    this.$message.error(res.message);
                }
            })
        },
        // 下架
        handleLower(goodsId) {
            this.$confirm('是否确认下架该商品', '提示', {}).then(() =>{
                shelves({ goodsIdList: [goodsId] }).then((res) => {
                    if (res.code == 200) {
                        this.refreshList()
                        this.$message({
                            type: 'success',
                            message: '下架成功!'
                        });
                    } else {
                        this.$message.error(res.message);
                    }
             })
            })

        },

        // 不审核编辑
        handleEdit(rowData){
            console.log('不审核编辑')
            this.$refs.openEditFrom.disabled = false;
            this.$refs.openEditFrom.formData({ ...rowData });
            this.$refs.openEditFrom.title = "编辑";
            this.$refs.openEditFrom.open = true;
            if(rowData.goodsType==1) {
                this.$refs.openEditFrom.types = 1
            } else {
                this.$refs.openEditFrom.types = 2
            }
        },

        //提交审核
        async handExamine(goodsId) {
            let res = await this.updateGoodsByGooodsId({
                goodsId,
                marketEnable: '3',
                authFlag: "2"
            }, "提交审核")
        },


        /**修改商品
         * marketEnable: 上架状态（1草稿 2上架 3下架）
         * authFlag: 1通过   2待审核   3驳回   4未提交
         */
        updateGoodsByGooodsId(param, message = '操作成功') {
            return new Promise((resolve, reject) => {
                updateGoods(param).then((res) => {
                    if (res.code == 200) {
                        this.refreshList()
                        this.$message({
                            message: message,
                            type: "success",
                        });
                        resolve(true)
                    } else {
                        this.$message.error(res.message);
                    }
                });
            })
        },

        //
        delGoods(goodsIdArr) {
            deleteGoodsByIds({ ids: goodsIdArr }).then((res) => {
                if (res.code == 200) {
                    this.queryParams.pageNum = repage(this.queryParams.pageSize, this.queryParams.pageNum, this.total)
                    this.refreshList()
                    this.$message({
                        message: "删除成功",
                        type: "success",
                    });
                }
            });
        },

        /** 查询列表 */
        getList() {
            this.loading = true;
            let form = { ...this.queryParams };
            delete form.categoryFirstName;
            delete form.categorySecondName;
            if(this.sourceStoreId){
              form.storeId=this.sourceStoreId
            }
            getGoodsList(form)
                .then((res) => {
                    this.loading = false;
                    if (res.code == 200) {
                        this.list = res.result.list;
                        this.total = Number(res.result.total);
                        this.imgList = res.result.list[0].images.split(',');
                        if (this.queryParams.marketEnable == 0) {
                            this.queryParams.marketEnable = ''
                        }

                    } else {
                        this.$message.error(res.message);
                    }
                })
                .catch(() => {
                    this.loading = false;
                });
        },

        //重组tab菜单列表
        getTabList() {
            // 商品状态数量统计
            selectGoodsCount({
                tenantId: this.$store.state.user.user.tenantId || '',
                putShell:'1',
                storeId:this.queryParams.storeId,
                createBy: this.createBy
            }).then((res) => {
                if (res.code == 200) {
                    const result = res.result;
                    // marketEnable:1草稿   2已上架 3已下架
                    // authFlag: 1通过   2待审核   3驳回   4未提交

                    if (result) {
                        const {
                            draftCount, dshelfCount, failCount, offshellCount, onSaleCount, totalCount, underReviewCount, upshellCount, sftgCount
                        } = result;
                        const newApproveStatusList = JSON.parse(JSON.stringify(this.approveStatusList));
                        const newShopStatus = JSON.parse(JSON.stringify(this.shopStatusList));
                        const tabList = [...newShopStatus, ...newApproveStatusList];
                        tabList.forEach((item, index) => {
                            item.idKey = index + item.dictCode
                            if (item.dictType == 'shop_status' && item.dictValue == 1) {
                                item.tabName = item.dictLabel //+ `（${draftCount}）`
                            } else if (item.dictType == 'shop_status' && item.dictValue == 2) {
                                item.tabName = item.dictLabel //+ `（${dshelfCount}）`
                            } else if (item.dictType == 'shop_status' && item.dictValue == 3) {
                                item.tabName = item.dictLabel //+ `（${offshellCount}）`
                            } else if (item.dictType == 'goods_approve_status' && item.dictValue == 1) {
                                item.tabName = item.dictLabel// + `（${sftgCount}）`
                            }
                            else if (item.dictType == 'goods_approve_status' && item.dictValue == 2) {
                                item.tabName = item.dictLabel //+ `（${underReviewCount}）`
                            }
                            else if (item.dictType == 'goods_approve_status' && item.dictValue == 3) {
                                item.tabName = item.dictLabel// + `（${failCount}）`
                            }
                        });
                        let allMen = {
                            tabName: '全部',// + `（${totalCount}）`,
                            idKey: '-51a',
                            dictType: "",
                            dictValue: '',
                            dictCode: '-1'
                        };
                        //
                         let putShelf = {
                            tabName: '待上架',// + `（${upshellCount}）`,
                            idKey: '26ka',
                            dictType: "marketEnable_authFlag",
                            dictValue: '',
                            dictCode: '-2',
                            marketEnable:'2',
                            authFlag:'1'
                        };

                        tabList.unshift(allMen);
                        tabList.splice(5,0,putShelf)
                        this.tabList = tabList;
                        this.goodsStaStatusCount = result;
                    }
                } else {
                    this.$message.error(res.message);
                }
            })
        },

        // 批量操作
        batchChange(val) {
            // marketEnable:1草稿   2已上架 3已下架
            // authFlag: 1通过   2待审核   3驳回   4未提交
            let selectData = this.multipleSelection || [];
            if (selectData.length == 0) {
                this.$message.error('请先选择你要操作的商品');
                this.batchStatus = '';
                return
            }
            let filterGoods = [];
            let allId = [];
            let handleTitle = '批量下架';
            if (val == 0) {
                handleTitle = '批量下架';
                filterGoods = selectData.filter((item) => {
                    return item.authFlag == 1 && item.marketEnable == 2
                })
            }
            if (val == 1) {
                handleTitle = '批量上架';
                filterGoods = selectData.filter((item) => {
                    return item.authFlag == 1 && item.marketEnable == 3
                })
            }
            if (val == 2) {
                handleTitle = '批量删除';
                filterGoods = selectData.filter((item) => {
                    return item.marketEnable != 2
                })
            };

            this.$refs.multipleTable.clearSelection();//取消选择


            filterGoods.forEach(item => {
                allId.push(item.goodsId);
                this.$refs.multipleTable.toggleRowSelection(item);//选择可以操作的
            });
            if (allId.length == 0) {
                this.$message({
                    type: 'error',
                    message: `没有可以${handleTitle}的商品`
                });
                this.batchStatus = '';
                this.$refs.multipleTable.clearSelection();
                return;
            };
            this.$confirm(`确定进行${handleTitle}操作吗?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 0批量下架
                if (val == 0) {
                    goodsUp({ goodsIdList: allId }).then((res) => {
                        if (res.code == 200) {
                            this.refreshList()
                            this.$refs.multipleTable.clearSelection();
                            this.batchStatus = '';
                            this.$message({
                                type: 'success',
                                message: handleTitle + '成功!'
                            });
                        } else {
                            this.$message.error(res.message);
                        }
                    })
                };
                //  1批量上架
                if (val == 1) {
                    goodsPut({ goodsId: allId }).then((res) => {
                        if (res.code == 200) {
                            this.refreshList()
                            this.$refs.multipleTable.clearSelection();
                            this.batchStatus = '';
                            this.$message({
                                type: 'success',
                                message: handleTitle + '成功!'
                            });
                        } else {
                            this.$message.error(res.message);
                        }
                    })
                };
                // 2批量删除
                if (val == 2) {
                    this.delGoods(allId);
                    this.batchStatus = '';
                };

            }).catch(() => {
                this.batchStatus = '';
                this.$refs.multipleTable.clearSelection();//取消选择
                this.multipleSelection = [];
                filterGoods = [];
                allId = [];
            })
        },

        /** 点击选项卡 */
        tabClick(tab) {
            const currentTab = this.tabList[tab.index];
            this.activeTab = currentTab.idKey;

            if (currentTab.dictType == '') {
                this.queryParams.authFlag = '';
                this.queryParams.marketEnable = '';
            };

             if (currentTab.dictType == 'marketEnable_authFlag') {
                this.queryParams.authFlag = '1';
                this.queryParams.marketEnable = '3';
            };

            if (currentTab.dictType == 'shop_status') {
                this.queryParams.authFlag = '';
                this.queryParams.marketEnable = currentTab.dictValue
            }

            if (currentTab.dictType == 'goods_approve_status') {
                this.queryParams.marketEnable = '';
                this.queryParams.authFlag = currentTab.dictValue
            }
            this.handleQuery();
        },
        /** 搜索按钮操作 */
        handleQuery() {

            const param = this.queryParams;
            if (param.minSalesNum != '' && param.maxSalesNum != '') {
                if (param.minSalesNum * 1 > param.maxSalesNum * 1) {
                    this.$message({
                        type: 'error',
                        message: '最低销售量不能大于最高销量，请重新填写'
                    });
                    return
                }
            }

            if (param.minPrice != '' && param.maxPrice != '') {
                if (param.minPrice * 1 > param.maxPrice * 1) {
                    this.$message({
                        type: 'error',
                        message: '最低价不能大于最高价，请重新填写'
                    });
                    return
                }
            }
            this.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.queryParams.categoryFirst = '';
            this.queryParams.categoryFirstName = '';
            this.queryParams.categoryIds = [];
            this.queryParams.categorySecond = '';
            this.queryParams.categorySecondName = '';
            this.queryParams.minPrice = '';
            this.queryParams.minSalesNum = ''
            this.queryParams.maxPrice = '';
            this.queryParams.maxSalesNum = ''
            this.resetForm("queryForm");
            this.handleQuery();
        },

        /**新增 */
        async handleAdd(type) {
            let shop = await this.getShop();
            if (!shop) {
                return
            }
            if(type==0){
              this.$router.push({
                path: '/goodsList/create',
                query: {
                    categoryType: type,

                },
              });

            }else{
              this.$router.push({
                path: '/goodsList/createServer',
                query: {
                    categoryType: type,

                },
              });
            }

            // this.$refs.openFrom.title = "新增";
            // this.$refs.openFrom.open = true;
            // this.$refs.openFrom.form.storeId = shop.storeId;
        },
        //详情
        handDetails(data) {
          if(data.goodsType==0){
            this.$refs.openFrom.showClose = true;
            this.$refs.openFrom.title = "商品详情";
            this.$refs.openFrom.authType = 1;
            this.$refs.openFrom.disabled = true;
            this.$refs.openFrom.open = true;
            this.$refs.openFrom.type = 1;
            this.$refs.openFrom.details(data.goodsId);
          }
          if(data.goodsType==1){
            this.$refs.openFrom1.showClose = true;
            this.$refs.openFrom1.authType = 1;
            this.$refs.openFrom1.title = "服务详情";
            this.$refs.openFrom1.disabled = true;
            this.$refs.openFrom1.open = true;
            this.$refs.openFrom.type = 2;
            this.$refs.openFrom1.details(data.goodsId);
          }


        },

        //编辑
        async edit(rowData) {
         let shop = await this.getShop();
         console.log(shop)
          console.log(rowData)
          if(rowData.goodsType==0){
            this.$router.push({
              path: '/goodsList/Edit',
              query:{goodsId:rowData.goodsId}
            });
          }else{
            this.$router.push({
              path: '/serversList/Edit',
              query:{goodsId:rowData.goodsId}
            });
          }


            return
            this.$refs.openFrom.disabled = false;
            this.$refs.openFrom.formData({ ...rowData });
            this.$refs.openFrom.title = "重新上架";
            this.$refs.openFrom.open = true;


        },
        // 选择商品菜单--获取相应的菜单name值
        categoryChange(v) {
            this.selectCategory.ids = v;
            console.log(v)
            if(v.length>0) {
                this.queryParams.categoryFirst = this.selectCategory.ids[0] || ''
                this.queryParams.categorySecond = this.selectCategory.ids[1] || ''
                var thsAreaCode = this.$refs['category'].getCheckedNodes()[0];
                this.selectCategory.name = thsAreaCode.pathLabels;
                this.queryParams.categoryFirst = this.selectCategory.ids[0] || ''

                this.queryParams.categoryFirstName = thsAreaCode.pathLabels[0] || ''
                this.queryParams.categorySecondName = thsAreaCode.pathLabels[1] || ''
            } else {
                this.queryParams.categoryFirst = this.selectCategory.ids[0] || ''
                this.queryParams.categorySecond = this.selectCategory.ids[1] || ''
            }

        },

        // 表格选择事件
        tableSelectionChange(val) {
            this.multipleSelection = val;
        }
    },
};
</script>
<style scoped>
.plcz {
    font-size: 14px;
    color: #606266;
}
/deep/ .el-tabs__item{
    padding: 0 15px;
}
</style>
