<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-row :gutter="10" class="mb8">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
          <el-row class="form_row">
            <el-col class="form_col">
              <el-form-item label="操作人员：" prop="operatorName">
                <el-input v-model="queryParams.operatorName" placeholder="请输入操作人员" clearable />
              </el-form-item>
              <el-form-item label="接种时间：">
                <el-date-picker
                  v-model="time"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
    </el-card>

    <!-- 列表 -->
    <el-card shadow="never">
      <el-table :data="tableData" border v-loading="loading" style="width: 100%" :height="tableHeight">
        <el-table-column type="index" align="center" width="60" label="序号" fixed="left" />
        <el-table-column label="疫苗种类" align="center" min-width="120" prop="vaccineName">
        </el-table-column>
        <el-table-column label="接种对象" align="center" min-width="120" prop="earTagNo"></el-table-column>
        <el-table-column label="接种数量" align="center" min-width="100" prop="vaccineDose"></el-table-column>
        <el-table-column label="操作人员" align="center" min-width="120" prop="userName"></el-table-column>
        <el-table-column label="接种时间" align="center" min-width="150" prop="operateTime"></el-table-column>
        <el-table-column label="操作" align="center" width="100" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="viewDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog title="免疫记录详情" :visible.sync="detailDialog.visible" width="600px" @close="closeDetail">
      <div v-if="detailData">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="疫苗种类">{{ firstNonEmpty([detailData.vaccineName, detailData.vaccineType, detailData.immuneType]) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="接种对象">{{ firstNonEmpty([detailData.targetName, detailData.target, detailData.livestockType]) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="接种数量">{{ formatCount(detailData) }}</el-descriptions-item>
          <el-descriptions-item label="操作人员">{{ firstNonEmpty([detailData.operatorName, detailData.createByName, detailData.createBy]) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="接种时间">{{ firstNonEmpty([detailData.immuneTime, detailData.createTime, detailData.operateTime]) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDetail">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { immunePage } from '@/api/nmb/inventory/index'
import { tableUi } from '@/utils/mixin/tableUi.js'

export default {
  name: 'DiseaseIndex',
  mixins: [tableUi],
  data() {
    return {
      time: undefined,
      queryParams: {
        operatorName: '',
        startTime: '',
        endTime: '',
        pageNum: 1,
        pageSize: 20,
      },
      loading: true,
      total: 0,
      tableData: [],
      detailDialog: {
        visible: false
      },
      detailData: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 列表查询
    getList() {
      this.loading = true
      immunePage(this.queryParams).then(res => {
        if (res.code === 200) {
          this.tableData = res.result?.list || []
          this.total = Number(res.result?.total || 0)
        } else {
          this.$message.error(res.message || '获取数据失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.startTime = this.time[0]
        this.queryParams.endTime = this.time[1]
      } else {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    resetQuery() {
      this.time = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 表单重置
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },
    // 查看详情
    viewDetail(row) {
      this.detailData = row
      this.detailDialog.visible = true
    },
    // 关闭详情
    closeDetail() {
      this.detailDialog.visible = false
      this.detailData = null
    },
    // 小工具：取第一个非空字段
    firstNonEmpty(arr) {
      return arr.find(v => v !== undefined && v !== null && v !== '')
    },
    // 接种时间拆分显示
    timeText(row) {
      const val = this.firstNonEmpty([row.immuneTime, row.createTime, row.operateTime])
      if (!val) return ''
      const [date, time] = String(val).split(' ')
      return { date, time }
    },
    // 接种数量格式化
    formatCount(row) {
      const count = this.firstNonEmpty([row.immuneCount, row.count, row.quantity, row.num])
      if (!count && count !== 0) return '-'
      return count + '头'
    }
  }
}
</script>

<style scoped lang='scss'>
</style>
