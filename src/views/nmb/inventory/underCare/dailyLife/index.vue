<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-row :gutter="10" class="mb8">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
          <el-row class="form_row">
            <el-col class="form_col">
              <el-form-item label="耳标号：" prop="earTagNo">
                <el-input v-model="queryParams.earTagNo" placeholder="请输入耳标号" clearable />
              </el-form-item>
              <el-form-item label="记录时间：">
                <el-date-picker v-model="time" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                  start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
    </el-card>

    <!-- 列表 -->
    <el-card shadow="never">
      <el-table :data="tableData" border v-loading="loading" style="width: 100%" :height="tableHeight">
        <el-table-column type="index" align="center" width="60" label="序号" fixed="left" />
        <el-table-column prop="earTagNo" label="耳标号" align="center" min-width="120" />
        <el-table-column label="活畜状态" align="center" min-width="120">
          <template slot-scope="scope">
            {{ statusName(scope.row.livestockSpiritState) }}
          </template>
        </el-table-column>
        <el-table-column prop="feedingStatus" label="采食情况" align="center" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ statusName(scope.row.livestockEatState) }}
          </template>
        </el-table-column>
        <el-table-column prop="abnormalStatus" label="异常情况" align="center" min-width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.livestockErrorDesc }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center" min-width="180" show-overflow-tooltip />
        <el-table-column label="记录人员" align="center" min-width="120" prop="operatePeopleName"></el-table-column>
        <el-table-column label="记录时间" align="center" min-width="150" prop="operateTime"></el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-card>
  </div>
</template>

<script>
import { dailyStatePage } from '@/api/nmb/inventory/index'
import { tableUi } from '@/utils/mixin/tableUi.js'
export default {
  name: 'DailyLifeIndex',
  mixins: [tableUi],
  data() {
    return {
      time: undefined,
      queryParams: {
        earTagNo: '',
        startTime: '',
        endTime: '',
        pageNum: 1,
        pageSize: 20,
      },
      loading: true,
      total: 0,
      tableData: [],
    }
  },
  created() {
    this.getList()
  },
  computed: {
    statusName(val) => {
      
    }
    // 活畜状态格式化
    /* statusName(val) {
      if (!val) return ''
      let value = ''
      if (val == 'livestockSpiritState') {
        const map = {
          1: '正常',
          2: '食欲不振',
          3: '异常活跃',
          4: '患病症状',
        }
        value = map[val]
      } else if (val == 'livestockEatState') {
        const map = {
          1: '正常采食',
          2: '采食减少',
          3: '采食增加',
          4: '完全不采食',
        }
        value = map[val]
      }
      return value
    }, */
  },
  methods: {
    // 列表查询
    getList() {
      this.loading = true
      dailyStatePage(this.queryParams).then(res => {
        if (res.code === 200) {
          this.tableData = res.result?.list || []
          this.total = Number(res.result?.total || 0)
        } else {
          this.$message.error(res.message || '获取数据失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 搜索
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.startTime = this.time[0]
        this.queryParams.endTime = this.time[1]
      } else {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置
    resetQuery() {
      this.time = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },

  }
}
</script>

<style scoped lang='scss'></style>
