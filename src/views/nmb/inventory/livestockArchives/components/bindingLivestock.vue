<template>
  <div>
    <el-dialog
      title="新增活畜"
      :visible.sync="dialog.openBindLivestock"
      width="1100px"
      :close-on-click-modal="false"
      @close="refuse"
      class="dialogEar"
      append-to-body
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="养殖场">
              <el-input v-model="pastureName" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="养殖场地址">
              <el-input v-model="pastureArea" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="选择圈舍" prop="penId">
              <el-select
                v-model="form.penId"
                clearable
                class="selectWidth"
                @change="selectPenId"
                :disabled="disable"
              >
                <el-option
                  v-for="(item,index) in penList"
                  :label="item.penName"
                  :value="item.penId"
                  :key="index"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="活畜类别" prop="typeId">
              <el-select
                v-model="form.typeId"
                clearable
                class="selectWidth"
                @change="selectCategory"
                :disabled="disable"
              >
                <el-option
                  v-for="(item,index) in animalsCategory"
                  :label="item.livestockName"
                  :value="item.livestockId"
                  :key="index"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="活畜品种" prop="varietiesId">
              <el-select
                v-model="form.varietiesId"
                clearable
                class="selectWidth"
                @change="selectType('1')"
                :disabled="disable"
              >
                <el-option
                  v-for="(item,index) in animalsVarieties"
                  :label="item.varietiesName"
                  :value="item.varietiesId"
                  :key="index"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="活畜类型" prop="categoryId">
              <el-select
                v-model="form.categoryId"
                clearable
                class="selectWidth"
                @change="selectType('2')"
                :disabled="disable"
              >
                <el-option
                  v-for="(item,index) in animalsType"
                  :label="item.categoryName"
                  :value="item.categoryId"
                  :key="index"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="新增活畜数量">
              <el-input-number v-model="bindNum" :min="1" :max="500"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <template>
              <el-popover
                placement="top"
                width="160"
                v-model="popShow" v-if="this.bindType == 'multi'">
                <p>确认要重置绑定的活畜列表吗？</p>
                <div style="text-align: right; margin: 0">
                  <el-button size="mini" type="text" @click="popShow = false">取消</el-button>
                  <el-button type="primary" size="mini" @click="selectLivestock">确定</el-button>
                </div>
                <el-button slot="reference" type="success">{{popBtnName}}</el-button>
              </el-popover>
              <el-button @click="selectLivestock" type="success" v-else>{{popBtnName}}</el-button>
            </template>
          </el-col>
          <el-col :span="8">
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="称重日期" prop="weightTime">
              <el-date-picker
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model="form.weightTime"
                type="date"
                placeholder="选择称重日期"
                class="selectWidth"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="bindNum > 1 ? '默认重量' : '当前重量'" prop="livestockWeight">
<!--              <el-input type="text" v-model="form.livestockWeight" @input="(val)=>{form.livestockWeight=handleNumberInput(val);}">-->
                <el-input type="number" v-model="form.livestockWeight">
                <template slot="append">千克</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="怀胎次数" v-if="showPregnanciesNum">
              <el-input type="number" v-model="form.pregnanciesNum"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="活畜月龄" prop="livestockAge">
              <el-select
                v-model="form.livestockAge"
                placeholder="请选择活畜月龄"
                clearable
                class="selectWidth"
              >
                <el-option
                  v-for="dict in dict.type.livestock_age"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model="form.birthday"
                type="date"
                placeholder="选择出生日期"
                class="selectWidth"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入栏日期" prop="inShedsTime">
              <el-date-picker
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model="form.inShedsTime"
                type="date"
                placeholder="选择入栏日期"
                class="selectWidth"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="耳标来源">
              <el-radio-group v-model="form.source" style="margin-top: 10px;" @change="selectChange">
                <el-radio label="1">系统生成</el-radio>
                <el-radio label="2">第三方</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8"></el-col>
          <el-col :span="8"></el-col>
        </el-row>
        <el-row v-if="bindNum > 1">
          <el-col :span="8">
            <el-form-item label="连续耳标">
              <el-radio-group v-model="earTagType" style="margin-top: 10px;">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="耳标区间" v-if="earTagType == '1'">
              <el-input type="number" v-model="batchEarTagStart" placeholder="起始编号">
                <template slot="prepend" v-if="prefix != ''">{{prefix}}</template>
              </el-input>
              <span style="padding: 0 5px;">至</span>
              <el-input type="number" v-model="batchEarTagEnd" placeholder="结束编号">
                <template slot="prepend" v-if="prefix != ''">{{prefix}}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="bindType == 'one'">
          <el-row>
            <el-col :span="8">
              <el-form-item label="耳标编号" prop="earTagNo">
                <el-input type="number" v-model="form.earTagNo" placeholder="请输入耳标编号">
                  <template slot="prepend" v-if="prefix != ''">{{prefix}}</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="母扣编号" prop="maleEarTagNo">
                <el-input type="number" v-model="form.maleEarTagNo" placeholder="请输入母扣编号"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="父亲编号类型" prop="fatherTagType">
                <el-select v-model="form.fatherTagType" placeholder="请选择" clearable class="selectWidth">
                  <el-option
                    v-for="dict in dict.type.livestock_tag_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="父亲编号" prop="fatherTagNo">
                <el-input v-model="form.fatherTagNo"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="母亲编号类型" prop="motherTagType">
                <el-select v-model="form.motherTagType" placeholder="请选择" clearable class="selectWidth">
                  <el-option
                    v-for="dict in dict.type.livestock_tag_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="母亲编号" prop="motherTagNo">
                <el-input v-model="form.motherTagNo"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
            </el-col>
          </el-row>
        </template>
        <!--        批量绑定-->
        <template v-if="bindType == 'multi'">
          <el-row><el-col :span="24" style="text-align: center;margin-bottom: 10px;font-weight: bold;">绑定的活畜列表</el-col></el-row>
            <el-table :data="livestockDataList" align="center" height="400" border style="width: 1000px;margin: 0 20px 20px 20px;padding: 10px;">
              <el-table-column
                prop="id"
                label="序号"
                width="100" align="center">
              </el-table-column>
              <el-table-column
                prop="earTagNo"
                label="耳标号"
                width="350" align="center">
                <template slot-scope="scope">
                  <el-input type="number" v-model="scope.row.earTagNo" style="width: 260px;margin: 10px;">
                    <template slot="prepend" v-if="prefix != ''">{{prefix}}</template>
                  </el-input>
                </template>
              </el-table-column>
              <el-table-column
                prop="livestockWeight"
                label="当前重量"
                width="350" align="center">
                <template slot-scope="scope">
<!--                  <el-input type="text" v-model="scope.row.livestockWeight" style="width: 260px;margin: 10px;" @input="(val)=>{scope.row.livestockWeight=handleNumberInput(val);}">-->
                    <el-input type="number" v-model="scope.row.livestockWeight" style="width: 260px;margin: 10px;">
                    <template slot="append">千克</template>
                  </el-input>
                </template>
              </el-table-column>
              <el-table-column
                label=""
                width="180" align="center">
                <template slot-scope="scope">
                  <el-popover
                    placement="top"
                    width="160"
                    v-model="scope.row.popShow">
                    <p>确定要删除吗？</p>
                    <div style="text-align: right; margin: 0">
                      <el-button size="mini" type="text" @click="scope.row.popShow = false">取消</el-button>
                      <el-button type="danger" size="mini" @click="delRow(scope)">确定</el-button>
                    </div>
                    <el-button type="danger" size="mini" icon="el-icon-delete" circle slot="reference"></el-button>
                  </el-popover>
                </template>
              </el-table-column>
            </el-table>
        </template>
        <el-row>
          <el-col :span="24">
            <el-form-item label="活畜图片" prop="livestockImage">
              <image-upload :disabled="disabled" v-model="form.livestockImage" :limit="5"></image-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="检疫证明" prop="quarantineReportUrl">
              <image-upload :disabled="disabled" v-model="form.quarantineReportUrl" :limit="5"></image-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>


<script>

  import {earTaglist, pasturePenList, pastureFenceCodeList} from "@/api/system/earTagManage";

  import {
    pastureById,
    earTagAdd,
    earTagBatch,
    livestockList,
    varietiesList,
    animalTypeList,
    earTagEdit
  } from "@/api/ffs/supervisionSheet/siteManagement.js";
  import {addLivestock} from "@/api/xmb/pasture/index.js";


  export default {
    dicts: ["livestock_age", "livestock_tag_type"],

    props: {
      dialog: {
        type: Object,
        default: {},
      },
    },
    computed:{
      popBtnName(){
        if(this.bindNum > 1){
          return "批量绑定";
        }else{
          return "单只绑定";
        }
      }
    },
    data() {
      let now = new Date().toISOString().slice(0, 10);
      return {
        prefix: "A", //耳标前缀
        pastureName: "", //养殖场
        pastureArea: "", //养殖场

        bindNum: 1,
        earTagType: "0",
        batchEarTagStart: null,
        batchEarTagEnd: null,
        bindType: "one", //one 单条 multi 多条

        popShow: false,
        options: [],
        disable: false,
        differEar: null,
        form: {
          source: "1", //耳标来源
          status: 1,
          inShedsType: 2,
          pastureId: "",
          userId: "",
          typeId: undefined,
          typeName: undefined,
          categoryId: undefined,
          categoryName: undefined,
          varietiesId: undefined,
          varietiesName: undefined,
          earTagNo: undefined,
          maleEarTagNo: undefined,
          livestockAge: undefined,
          livestockAgeValue: undefined,
          birthday: undefined,
          fatherTagType: '0',
          fatherTagNo: undefined,
          motherTagType: '0',
          motherTagNo: undefined,
          livestockImage: undefined,
          quarantineReportUrl: "",
          penId: "", // 圈舍id
          fenceCode: "", // 栏位编号
          pregnanciesNum: undefined, // 怀胎次数
          livestockWeight: undefined,
          weightTime: now,
          inShedsTime: undefined
        },
        showPregnanciesNum: false,
        animalsCategory: [], //活畜类别
        animalsVarieties: [], //品种
        animalsType: [], //类型
        selectList: [],
        livestockDataList: [],
        disabled: false,
        rules:{
          penId: [ { required: true, message: "请选择圈舍", trigger: "blur" } ],
          typeId: [ { required: true, message: "请选择活畜类别", trigger: "blur" } ],
          categoryId: [ { required: true, message: "请选择活畜类型", trigger: "blur" } ],
          varietiesId: [ { required: true, message: "请选择活畜品种", trigger: "blur" } ],
          livestockImage: [ { required: true, message: "请上传活畜图片", trigger: "blur" } ],
          weightTime: [ { required: true, message: "请输入称重日期", trigger: "blur" } ]
        },
        penList: [],
        fenceList: [],
      };
    },

    created() {
      this.form.pastureId = this.$route.query.pastureId;
      console.log('pastureId');
      console.log(this.$route.query.pastureId)
      console.log('pastureId');
      this.getCategory();

      this.getPasturebyId();

      this.$nextTick(() => {
        this.getVarieties(this.form.typeId);

        this.getType(this.form.typeId);
      });
      this.getPasturePenList()
      this.setRules();
    },

    methods: {
      safeParseInt(input){
        if (typeof input !== 'string') {
          return 0;
        }

        const trimmedInput = input.trim();
        if (trimmedInput === '') {
          return 0;
        }

        const parsedNumber = parseInt(trimmedInput);
        if (isNaN(parsedNumber)) {
          return 0;
        }

        return parsedNumber;
      },
      handleNumberInput(val){
        let num = this.safeParseInt(val);
        return ((num <= 0) || num > 900000000) ? "" : num.toString();
      },
      //活畜养殖场信息
      setRules(){
        // if(this.bindType == "one"){
        //   if(!this.rules.livestockWeight){
        //     this.rules.livestockWeight = [ { required: true, message: "请输入当前重量", trigger: "blur" } ];
        //   }
        // }else{
        //   if(this.rules.livestockWeight) {
        //     delete this.rules.livestockWeight;
        //   }
        // }
        // console.log(this.rules)
      },
      getPasturebyId() {
        pastureById({ids: [this.form.pastureId]}).then((res) => {
          if (res.code == 200) {
            this.form.userId = res.result.userId;

            this.pastureName = res.result.pastureName;

            this.pastureArea =
              res.result.provinceName +
              res.result.cityName +
              res.result.countyName;
          }
        });
      },
      sectEarTag() {
        this.differEar = null
        if (this.form.earTagEnd && this.form.earTagStart) {
          if (parseInt(this.form.earTagStart) >= parseInt(this.form.earTagEnd)) {
            this.$modal.msgWarning("起始编号不能大于等于结束编号");
            return
          }
          if (parseInt(this.form.earTagEnd) - parseInt(this.form.earTagStart) > 500) {
            this.$modal.msgWarning("耳标数量不能大于500");

            return
          }
          this.differEar = parseInt(this.form.earTagEnd) - parseInt(this.form.earTagStart) + 1
        }

      },
      selectChange() {
        this.options = [];

        if (this.activeName == 1) {
          this.form.earTagNo = "";
        } else {
          this.form.earTagEnd = "";

          this.form.earTagStart = "";
        }

        this.form.source == 1 ? (this.prefix = "A") : (this.prefix = "");
      },

      tagNoChange() {
        this.form.earTagUid = this.form.earTagNo.earTagUid;

        this.form.earTagQrCode = this.form.earTagNo.earTagQrCode;

        if (this.form.source == 1) {
          this.form.earTagNo = this.form.earTagNo.earTagNo.slice(1);
        } else {
          this.form.earTagNo = this.form.earTagNo.earTagNo;
        }
      },

      //搜索耳标

      remoteMethod(value) {
        earTaglist({
          pageNum: 1,

          pageSize: 10,

          statusStr: "1,2",

          source: this.form.source,

          earTagNo: this.prefix + value,
        }).then((res) => {
          if (res.code == 200) {
            this.options = res.result.list;
          }
        });
      },
      submitForm() {
        this.$refs["ruleForm"].validate(async (valid) => {
          if (!valid) {
            return;
          }
          let data = {};
          let copyFields = ["source", "status", "inShedsType", "pastureId", "penId", "userId", "typeId", "typeName", "categoryId", "categoryName", "varietiesId", "varietiesName", "birthday", "livestockAge", "inShedsTime", "weightTime", "livestockImage", "quarantineReportUrl"];
          copyFields.forEach((field)=>{
            data[field] = this.form[field];
          })
          let bindLivestockList = [];
          if(this.bindType == "multi"){
            for (let i=0; i<this.livestockDataList.length; i++){
              let item = this.livestockDataList[i];
              if(!item.livestockWeight || item.livestockWeight <= 0){
                this.$modal.msgWarning("绑定的活畜列表中当前重量信息不能为空！");
                return;
              }
              let newItem = {};
              newItem.livestockWeight = item.livestockWeight.toString();
              newItem.earTagNo = (!item.earTagNo) ? "" : ((this.form.source === "1") ? "A"+item.earTagNo.toString() : item.earTagNo.toString());
              bindLivestockList.push(newItem);
            }
          }else if(this.bindType == "one"){
            if(!this.form.livestockWeight){
              this.$modal.msgWarning("当前重量不能为空！");
              return;
            }
            let motherTagType = this.form.motherTagType;
            let fatherTagType = this.form.fatherTagType;
            if(!this.form.motherTagNo){
              motherTagType = undefined;
            }
            if(!this.form.fatherTagNo){
              fatherTagType = undefined;
            }
            let newEarTag = "";
            if(this.form.earTagNo){
              newEarTag = this.form.earTagNo.toString();
              if(this.form.source === "1"){
                newEarTag = "A"+newEarTag;
              }
            }
            bindLivestockList.push({
              earTagNo: newEarTag,
              maleEarTagNo: this.form.maleEarTagNo,
              fatherTagType: fatherTagType,
              fatherTagNo: this.form.fatherTagNo,
              motherTagType: motherTagType,
              motherTagNo: this.form.motherTagNo,
              livestockWeight: this.form.livestockWeight
            });
          }else{
            this.$modal.msgWarning("参数构造错误！");
            return;
          }
          if(bindLivestockList.length <= 0){
            this.$modal.msgWarning("绑定的活畜重量信息不能为空！");
            return;
          }
          data.livestockList = bindLivestockList;
          data = JSON.parse(JSON.stringify(data));
          this.$modal.loading("正在提交，请稍候...");

          // console.log(data, "submit form data")
          // setTimeout(()=>{
          //   this.$modal.closeLoading();
          // }, 3000)

          addLivestock(data).then((res) => {
            if (res.code == 200) {
              this.$modal.closeLoading();
              this.$message({
                type: "success",
                message: "添加成功",
              });
              this.refuse();
            }
          }).catch(() => {
            this.$modal.closeLoading();
          });

        });
      },
      selectLivestock(){
        if(this.bindNum > 1){
          let iEarTagStart = -1;
          let earTagNum = 0;
          let fillZero = false;
          const regex = /^0+/;
          let len;
          if(this.earTagType == "1"){
            iEarTagStart = this.batchEarTagStart ? parseInt(this.batchEarTagStart) : -1;
            let iEarTagEnd = this.batchEarTagEnd ? parseInt(this.batchEarTagEnd) : -1;
            if(iEarTagStart > 0 && iEarTagEnd > 0){
              earTagNum = iEarTagEnd-iEarTagStart;
              if(earTagNum <= 0){
                this.$modal.msgWarning("起始编号不能大于等于结束编号");
                return
              }
              if(earTagNum > 200){
                this.$modal.msgWarning("耳标数量不能大于200");
                return
              }
              len = this.batchEarTagStart.length;
              if(regex.test(this.batchEarTagStart) && len === this.batchEarTagEnd.length){
                fillZero = true;
              }
            }
          }

          this.livestockDataList = [];
          for (let i=0; i<this.bindNum; i++){
            let id = i+1;
            let earTag = null;
            if(iEarTagStart > 0 && earTagNum >= i){
              earTag = iEarTagStart+i;
            }
            if(fillZero){
              const numberStr = earTag.toString();
              // 使用 padStart 方法确保字符串长度为 totalLength，不足的部分用 0 填充
              earTag = numberStr.padStart(len, '0');
            }
            this.livestockDataList.push({
              id: id, livestockWeight: this.form.livestockWeight, earTagNo: earTag, popShow: false
            })
          }
          this.bindType = "multi";
        }else if(this.bindNum == 1){
          this.bindType = "one";
        }
        this.setRules();
        this.popShow = false;
      },
      refuse() {
        this.$emit("close");
        this.$emit("refresh");
      },
      delRow(scope){
        let index = scope.$index;
        let row = scope.row;
        console.log(row, index, "del row")
        this.livestockDataList.splice(index, 1);
      },
      //获取活畜信息
      getCategory() {
        livestockList({pageNum: 1, pageSize: 100000}).then((res) => {
          this.animalsCategory = res.result;

          this.animalsCategory.forEach((item) => {
            if (item.livestockName == '牛') {
              this.form.typeId = item.livestockId;
              this.form.typeName = item.livestockName;
              this.selectCategory();
            }
          });

        });
      },

      getVarieties(typeId) {
        animalTypeList({
          pageNum: 1,

          pageSize: 100000,

          categoryType: typeId,
        }).then((res) => {
          this.animalsType = res.result;
        });
      },
      //或者品种
      getType(typeId) {
        varietiesList({
          pageNum: 1,

          pageSize: 100000,

          categoryType: typeId,
        }).then((res) => {
          this.animalsVarieties = res.result;
        });
      },
      selectType(type, val) {
        if (type == 1) {
          this.animalsVarieties.forEach((item) => {
            if (this.form.varietiesId == item.varietiesId) {
              this.form.varietiesName = item.varietiesName;
            }
          });
        }

        if (type == 2) {
          this.animalsType.forEach((item) => {
            if (this.form.categoryId == item.categoryId) {
              this.form.categoryName = item.categoryName;
              console.log(this.form.categoryName)
              if (this.form.categoryName.includes('母')) {
                this.showPregnanciesNum = true
              } else {
                this.showPregnanciesNum = false
              }
            }
          });
        }
      },

      selectCategory() {
        this.form.varietiesId = "";

        this.form.categoryId = "";

        this.getVarieties(this.form.typeId);

        this.getType(this.form.typeId);

        this.animalsCategory.forEach((item) => {
          if (this.form.typeId == item.livestockId) {
            this.form.typeName = item.livestockName;
          }
        });
      },
      // 获取当前牧场圈舍
      getPasturePenList() {
        pasturePenList({pastureId: this.form.pastureId || '', pid: 0}).then(res => {
          console.log(res)
          if (res.code == 200) {
            this.penList = res.result || []
          }
        })
      },

      // 获取当前圈舍栏位
      selectPenId() {
        this.form.fenceCode = ''
        console.log(this.form.penId)
        pastureFenceCodeList({penId: this.form.penId}).then(res => {
          console.log(res)
          if (res.code == 200) {
            this.fenceList = res.result || []
          }
        })
      }
    },
  };
</script>


<style lang="scss">
  .dialogEar {
    .el-dialog__header {
      background-color: #f4f4f4;
    }

    .earTagNo {
      .el-input__prefix {
        left: 22px;
      }
    }

    .el-dialog__footer {
      text-align: center;
    }

    .el-form-item__content {
      display: flex;
    }

    .selectWidth {
      width: 100%;
    }
  }

  .inputWidth {
    width: 100%;
  }
</style>

